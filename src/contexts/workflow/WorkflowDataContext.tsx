import { createContext, useContext, useState, ReactNode, useMemo, useCallback } from 'react'
import { IOrganizationSchemaParams } from '@/types'

export interface WorkflowDataContext {
  data: unknown
  setData: React.Dispatch<React.SetStateAction<any>>
  initialData: any
  setInitialData: React.Dispatch<React.SetStateAction<any>>
  isDirty: boolean
  attachments: unknown[]
  setAttachments: (_attachments: unknown[]) => void
  updateEntity: (_field: string, _value: any) => void
  setUpdateEntity: React.Dispatch<React.SetStateAction<any>>
  orgTreeInitialData: IOrganizationSchemaParams
  setOrgTreeInitialData: React.Dispatch<React.SetStateAction<IOrganizationSchemaParams>>
}

const WorkflowDataContextInstance = createContext<WorkflowDataContext | undefined>(undefined)

export const useWorkflowData = () => {
  const context = useContext(WorkflowDataContextInstance)
  if (!context) throw new Error('useWorkflowData must be used within a WorkflowDataProvider')
  return context
}

export const WorkflowDataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [data, setData] = useState<any>(null)
  const [initialData, setInitialData] = useState<any>(null)
  const [attachments, setAttachments] = useState<any[]>([])
  const [orgTreeInitialData, setOrgTreeInitialData] = useState<IOrganizationSchemaParams>({
    departments: [],
    selectedDepartment: undefined,
    divisions: [],
    selectedDivision: undefined,
    units: [],
    selectedUnit: undefined,
    teams: [],
    selectedTeam: undefined,
    subTeams: [[]],
    selectedSubTeams: [],
    users: [],
  })

  // Calculate if data is dirty (different from initial data)
  const isDirty = useMemo(() => {
    if (!initialData ?? !data) return false
    return JSON.stringify(data) !== JSON.stringify(initialData)
  }, [data, initialData])

  // Function to update a specific field in the data object
  const updateEntity = useCallback((field: string, value: any) => {
    setData((prevData: any) => ({
      ...prevData,
      [field]: value,
    }))
  }, [])

  const value = useMemo(
    () => ({
      data,
      setData,
      initialData,
      setInitialData,
      isDirty,
      attachments,
      setAttachments,
      updateEntity,
      setUpdateEntity: setData, // Alias for backward compatibility
      orgTreeInitialData,
      setOrgTreeInitialData,
    }),
    [data, initialData, isDirty, attachments, updateEntity, orgTreeInitialData],
  )

  return <WorkflowDataContext.Provider value={value}>{children}</WorkflowDataContext.Provider>
}

// Export context for other providers to use
export { WorkflowDataContext }
